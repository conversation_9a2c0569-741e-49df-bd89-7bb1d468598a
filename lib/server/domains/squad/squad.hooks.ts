import { Squad, SquadMember, UserSquad } from "@/lib/domains/squad/squad.types"
import { SquadServerService } from "./squad.service"

/**
 * Server-side Squad Hooks using Firebase Admin SDK
 * These hooks are designed for server-side operations and require userId parameter
 * since they cannot access client-side auth stores
 */
export class SquadServerHooks {
  /**
   * Get a squad by ID
   * @param squadId Squad ID
   * @returns The squad data or null if not found
   */
  static async useSquad(squadId: string): Promise<Squad | null> {
    try {
      return await SquadServerService.getSquad(squadId)
    } catch (error) {
      console.error("Error in useSquad (server):", error)
      return null
    }
  }

  /**
   * Get multiple squads by IDs
   * @param squadIds Array of squad IDs
   * @returns Array of squads
   */
  static async useSquadsFromIds(squadIds: string[]): Promise<Squad[]> {
    try {
      return await SquadServerService.getSquadsFromIds(squadIds)
    } catch (error) {
      console.error("Error in useSquadsFromIds (server):", error)
      return []
    }
  }

  /**
   * Get squads for a user
   * @param userId User ID
   * @returns Array of squads
   */
  static async useUserSquads(userId: string): Promise<Squad[]> {
    try {
      return await SquadServerService.getUserSquads(userId)
    } catch (error) {
      console.error("Error in useUserSquads (server):", error)
      return []
    }
  }

  /**
   * Get squad members
   * @param squadId Squad ID
   * @returns Array of squad members
   */
  static async useSquadMembers(squadId: string): Promise<SquadMember[]> {
    try {
      return await SquadServerService.getSquadMembers(squadId)
    } catch (error) {
      console.error("Error in useSquadMembers (server):", error)
      return []
    }
  }

  /**
   * Get user's squad memberships
   * @param userId User ID
   * @returns Array of user squad memberships
   */
  static async useUserSquadMemberships(userId: string): Promise<UserSquad[]> {
    try {
      return await SquadServerService.getUserSquadMemberships(userId)
    } catch (error) {
      console.error("Error in useUserSquadMemberships (server):", error)
      return []
    }
  }

  /**
   * Check if user is a member of a squad
   * @param userId User ID
   * @param squadId Squad ID
   * @returns True if user is a member
   */
  static async useIsUserMemberOfSquad(userId: string, squadId: string): Promise<boolean> {
    try {
      return await SquadServerService.isUserMemberOfSquad(userId, squadId)
    } catch (error) {
      console.error("Error in useIsUserMemberOfSquad (server):", error)
      return false
    }
  }

  /**
   * Check if user is a leader of a squad
   * @param userId User ID
   * @param squadId Squad ID
   * @returns True if user is a leader
   */
  static async useIsUserLeaderOfSquad(userId: string, squadId: string): Promise<boolean> {
    try {
      return await SquadServerService.isUserLeaderOfSquad(userId, squadId)
    } catch (error) {
      console.error("Error in useIsUserLeaderOfSquad (server):", error)
      return false
    }
  }

  /**
   * Get squads where user is a leader
   * @param userId User ID
   * @returns Array of squads where user is leader
   */
  static async useUserLeaderSquads(userId: string): Promise<Squad[]> {
    try {
      return await SquadServerService.getUserLeaderSquads(userId)
    } catch (error) {
      console.error("Error in useUserLeaderSquads (server):", error)
      return []
    }
  }

  /**
   * Get squad member count
   * @param squadId Squad ID
   * @returns Number of members
   */
  static async useSquadMemberCount(squadId: string): Promise<number> {
    try {
      return await SquadServerService.getSquadMemberCount(squadId)
    } catch (error) {
      console.error("Error in useSquadMemberCount (server):", error)
      return 0
    }
  }

  /**
   * Get user's squad statistics
   * @param userId User ID
   * @returns Squad statistics
   */
  static async useUserSquadStats(userId: string): Promise<{
    totalSquads: number
    leaderSquads: number
    memberSquads: number
  }> {
    try {
      return await SquadServerService.getUserSquadStats(userId)
    } catch (error) {
      console.error("Error in useUserSquadStats (server):", error)
      return {
        totalSquads: 0,
        leaderSquads: 0,
        memberSquads: 0,
      }
    }
  }

  /**
   * Get user's squads with trip counts (useful for dashboard)
   * @param userId User ID
   * @returns Array of squads with additional metadata
   */
  static async useUserSquadsWithMetadata(userId: string): Promise<Array<Squad & {
    memberCount: number
    isLeader: boolean
    role: "leader" | "member"
  }>> {
    try {
      const [squads, memberships] = await Promise.all([
        SquadServerService.getUserSquads(userId),
        SquadServerService.getUserSquadMemberships(userId),
      ])

      // Create a map of squad memberships for quick lookup
      const membershipMap = new Map(memberships.map(m => [m.squadId, m]))

      // Enhance squads with metadata
      const enhancedSquads = await Promise.all(
        squads.map(async (squad) => {
          const membership = membershipMap.get(squad.id)
          const memberCount = await SquadServerService.getSquadMemberCount(squad.id)

          return {
            ...squad,
            memberCount,
            isLeader: squad.leaderId === userId,
            role: membership?.role || "member",
          }
        })
      )

      return enhancedSquads
    } catch (error) {
      console.error("Error in useUserSquadsWithMetadata (server):", error)
      return []
    }
  }

  /**
   * Get squad with member details (useful for squad management)
   * @param squadId Squad ID
   * @returns Squad with member details or null
   */
  static async useSquadWithMembers(squadId: string): Promise<(Squad & {
    members: SquadMember[]
    memberCount: number
  }) | null> {
    try {
      const [squad, members] = await Promise.all([
        SquadServerService.getSquad(squadId),
        SquadServerService.getSquadMembers(squadId),
      ])

      if (!squad) {
        return null
      }

      return {
        ...squad,
        members,
        memberCount: members.length,
      }
    } catch (error) {
      console.error("Error in useSquadWithMembers (server):", error)
      return null
    }
  }

  /**
   * Get user's dashboard squad data (optimized for dashboard display)
   * @param userId User ID
   * @returns Dashboard-optimized squad data
   */
  static async useUserDashboardSquads(userId: string): Promise<{
    squads: Array<Squad & {
      memberCount: number
      isLeader: boolean
      role: "leader" | "member"
    }>
    stats: {
      totalSquads: number
      leaderSquads: number
      memberSquads: number
    }
  }> {
    try {
      const [squadsWithMetadata, stats] = await Promise.all([
        this.useUserSquadsWithMetadata(userId),
        SquadServerService.getUserSquadStats(userId),
      ])

      return {
        squads: squadsWithMetadata,
        stats,
      }
    } catch (error) {
      console.error("Error in useUserDashboardSquads (server):", error)
      return {
        squads: [],
        stats: {
          totalSquads: 0,
          leaderSquads: 0,
          memberSquads: 0,
        },
      }
    }
  }
}
