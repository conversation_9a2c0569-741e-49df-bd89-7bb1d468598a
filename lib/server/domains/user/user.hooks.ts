import { User } from "@/lib/domains/user/user.types"
import { UserServerService } from "./user.service"

/**
 * Server-side User Hooks using Firebase Admin SDK
 * These hooks are designed for server-side operations
 */
export class UserServerHooks {
  /**
   * Get a user by ID
   * @param userId User ID
   * @returns The user data or null if not found
   */
  static async useUser(userId: string): Promise<User | null> {
    try {
      return await UserServerService.getUser(userId)
    } catch (error) {
      console.error("Error in useUser (server):", error)
      return null
    }
  }

  /**
   * Get multiple users by IDs
   * @param userIds Array of user IDs
   * @returns Array of users
   */
  static async useUsersFromIds(userIds: string[]): Promise<User[]> {
    try {
      return await UserServerService.getUsersFromIds(userIds)
    } catch (error) {
      console.error("Error in useUsersFromIds (server):", error)
      return []
    }
  }

  /**
   * Get a user by email
   * @param email User email
   * @returns The user data or null if not found
   */
  static async useUserByEmail(email: string): Promise<User | null> {
    try {
      return await UserServerService.getUserByEmail(email)
    } catch (error) {
      console.error("Error in useUserByEmail (server):", error)
      return null
    }
  }

  /**
   * Check if user exists
   * @param userId User ID
   * @returns True if user exists
   */
  static async useUserExists(userId: string): Promise<boolean> {
    try {
      return await UserServerService.userExists(userId)
    } catch (error) {
      console.error("Error in useUserExists (server):", error)
      return false
    }
  }

  /**
   * Check if user is admin
   * @param userId User ID
   * @returns True if user is admin
   */
  static async useIsUserAdmin(userId: string): Promise<boolean> {
    try {
      return await UserServerService.isUserAdmin(userId)
    } catch (error) {
      console.error("Error in useIsUserAdmin (server):", error)
      return false
    }
  }

  /**
   * Check if user is new user
   * @param userId User ID
   * @returns True if user is new
   */
  static async useIsNewUser(userId: string): Promise<boolean> {
    try {
      return await UserServerService.isNewUser(userId)
    } catch (error) {
      console.error("Error in useIsNewUser (server):", error)
      return false
    }
  }

  /**
   * Get user profile data (safe for public display)
   * @param userId User ID
   * @returns Public user profile data
   */
  static async useUserProfile(userId: string): Promise<{
    uid: string
    displayName: string
    photoURL?: string | null
    bio?: string
    location?: string
  } | null> {
    try {
      const user = await UserServerService.getUser(userId)
      if (!user) {
        return null
      }

      return {
        uid: user.uid,
        displayName: user.displayName,
        photoURL: user.photoURL,
        bio: user.bio,
        location: user.location,
      }
    } catch (error) {
      console.error("Error in useUserProfile (server):", error)
      return null
    }
  }

  /**
   * Get user display name
   * @param userId User ID
   * @returns User display name or fallback
   */
  static async useUserDisplayName(userId: string): Promise<string> {
    try {
      const user = await UserServerService.getUser(userId)
      return user?.displayName || user?.email || "Unknown User"
    } catch (error) {
      console.error("Error in useUserDisplayName (server):", error)
      return "Unknown User"
    }
  }

  /**
   * Get user email
   * @param userId User ID
   * @returns User email or null
   */
  static async useUserEmail(userId: string): Promise<string | null> {
    try {
      const user = await UserServerService.getUser(userId)
      return user?.email || null
    } catch (error) {
      console.error("Error in useUserEmail (server):", error)
      return null
    }
  }

  /**
   * Get user statistics
   * @param userId User ID
   * @returns User statistics
   */
  static async useUserStats(userId: string): Promise<{
    isAdmin: boolean
    isNewUser: boolean
    hasCompletedProfile: boolean
    joinDate: Date | null
  }> {
    try {
      const user = await UserServerService.getUser(userId)
      if (!user) {
        return {
          isAdmin: false,
          isNewUser: false,
          hasCompletedProfile: false,
          joinDate: null,
        }
      }

      return {
        isAdmin: user.isAdmin === true,
        isNewUser: user.newUser === true,
        hasCompletedProfile: Boolean(user.displayName && user.email),
        joinDate: user.createdAt ? user.createdAt.toDate() : null,
      }
    } catch (error) {
      console.error("Error in useUserStats (server):", error)
      return {
        isAdmin: false,
        isNewUser: false,
        hasCompletedProfile: false,
        joinDate: null,
      }
    }
  }

  /**
   * Get current user data for dashboard (optimized for dashboard display)
   * @param userId User ID
   * @returns Dashboard-optimized user data
   */
  static async useCurrentUserDashboard(userId: string): Promise<{
    user: User | null
    stats: {
      isAdmin: boolean
      isNewUser: boolean
      hasCompletedProfile: boolean
      joinDate: Date | null
    }
  }> {
    try {
      const [user, stats] = await Promise.all([
        UserServerService.getUser(userId),
        this.useUserStats(userId),
      ])

      return {
        user,
        stats,
      }
    } catch (error) {
      console.error("Error in useCurrentUserDashboard (server):", error)
      return {
        user: null,
        stats: {
          isAdmin: false,
          isNewUser: false,
          hasCompletedProfile: false,
          joinDate: null,
        },
      }
    }
  }
}
