import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { getAdminAuth } from "@/lib/firebase-admin"
import { TripServerHooks } from "@/lib/server/domains/trip/trip.hooks"
import { UserServerHooks } from "@/lib/server/domains/user/user.hooks"
import { DashboardProvider } from "./components/DashboardProvider"
import { DashboardNavigation } from "./components/DashboardNavigation"
import { TestDashboardDemoTour } from "./components/TestDashboardDemoTour"

/**
 * Server-side authentication and data fetching for test dashboard
 */
async function getServerSideData() {
  try {
    // Get the auth token from cookies
    const cookieStore = await cookies()
    const authToken = cookieStore.get("firebase-auth-token")?.value

    if (!authToken) {
      redirect("/login")
    }

    // Verify the token and get user ID
    const adminAuth = await getAdminAuth()
    const decodedToken = await adminAuth.verifyIdToken(authToken)
    const userId = decodedToken.uid

    if (!userId) {
      redirect("/login")
    }

    // Fetch user data and dashboard data in parallel
    const [user, userTripsData] = await Promise.allSettled([
      UserServerHooks.useCurrentUserDashboard(userId),
      TripServerHooks.useUserAllTrips(userId),
    ])

    // Extract results with fallbacks
    const userData =
      user.status === "fulfilled"
        ? user.value
        : {
            user: null,
            stats: { isAdmin: false, isNewUser: false, hasCompletedProfile: false, joinDate: null },
          }
    const tripsData =
      userTripsData.status === "fulfilled"
        ? userTripsData.value
        : { trips: [], upcomingTrips: [], pastTrips: [] }

    // For now, we'll fetch squads data on the client side since we don't have the server service ready
    // TODO: Add squad server service and fetch squads here
    const squadsData = { squads: [], stats: { totalSquads: 0, leaderSquads: 0, memberSquads: 0 } }

    return {
      user: userData.user,
      userStats: userData.stats,
      trips: tripsData.trips,
      upcomingTrips: tripsData.upcomingTrips,
      pastTrips: tripsData.pastTrips,
      squads: squadsData.squads,
      squadStats: squadsData.stats,
      userId,
    }
  } catch (error) {
    console.error("Error in getServerSideData:", error)
    redirect("/login")
  }
}

/**
 * Test Dashboard Layout with SSR data fetching
 */
export default async function TestDashboardLayout({ children }: { children: React.ReactNode }) {
  const serverData = await getServerSideData()

  return (
    <DashboardProvider initialData={serverData}>
      <div className="min-h-screen bg-background">
        {/* Dashboard Header */}
        <div className="border-b bg-card">
          <div className="container mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold">Test Dashboard</h1>
                <p className="text-muted-foreground">
                  Welcome back, {serverData.user?.displayName || "Friend"}!
                </p>
              </div>
              <div className="text-sm text-muted-foreground">SSR + Realtime Sync</div>
            </div>
          </div>
        </div>

        {/* Dashboard Navigation */}
        <div className="border-b bg-card">
          <div className="container mx-auto px-6">
            <DashboardNavigation />
          </div>
        </div>

        {/* Dashboard Content */}
        <div className="container mx-auto px-6 py-6">{children}</div>

        {/* Demo Tour Modal */}
        <TestDashboardDemoTour />
      </div>
    </DashboardProvider>
  )
}
